﻿#ifndef IO_TABLE_VIEW_BIG_H
#define IO_TABLE_VIEW_BIG_H

#include <QAction>
#include <QDebug>
#include <QFile>
#include <QMenu>
#include <QStandardItemModel>
#include <QTranslator>
#include <QWidget>

#include "../io_convert_xls/io_convert_xls.h"
#include "../io_plot_widget/io_plot_widget.h"
#include "../io_xml_parser/io_xml_parser.h"
#include "io_custom_widgets_global.h"
#include "my_table_model_big.h"

namespace Ui {
class IoTableViewBig;
}

class IO_CUSTOM_WIDGETSSHARED_EXPORT IoTableViewBig : public QWidget
{
    Q_OBJECT

public:
    explicit IoTableViewBig(QWidget* parent = 0);
    ~IoTableViewBig();

private:
    Ui::IoTableViewBig* ui;

public:
    void set_title_list(QVector<QString> title_list);
    void set_column_width(int index, int width);
    //-------------------------------------------------
    void append_row(QVector<QString> row_data);
    //-------------------------------------------------
    QVector<QString>          get_selected_row();
    QVector<QVector<QString>> get_selected_rows();      // QTableView::SelectRows模式可用
    QVector<QVector<QString>> get_selected_rows_ex();   // QTableView::SelectItems模式可用
    QStringList               get_title_list();
    //-------------------------------------------------
    MyTableModelBig* get_table_model() { return my_table_model_; }   // 获取表格模型的访问方法


private:
    void init_view();
    void init_menu();
    void init_timer();
    void init_connect();

signals:
    void sig_selected_row(QVector<QString> row_data);
    void sig_doubleclick_row(QVector<QString> row_data);
    void sig_column_moved(QStringList titles);

private:
    MyTableModelBig* my_table_model_ = nullptr;
    QMenu*           context_menu_   = nullptr;
    QAction*         action_plot_    = nullptr;
};

#endif   // IO_TABLE_VIEW_H
