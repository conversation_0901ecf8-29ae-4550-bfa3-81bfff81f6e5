#include "io_log_analyzer.h"
#include "ui_io_log_analyzer.h"

IoLogAnalyzer::IoLogAnalyzer(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::IoLogAnalyzer)
{
    ui->setupUi(this);
    init_connect();
}

IoLogAnalyzer::~IoLogAnalyzer()
{
    //
    delete ui;
}

void IoLogAnalyzer::init(QString project)
{
    project_ = project;
    //
    loading_ = new LoadLogThread;
    thread_  = new QThread;
    loading_->moveToThread(thread_);
    thread_->start();
}

void IoLogAnalyzer::init_connect()
{
    // 选择日志文件
    connect(ui->PB_Select_File, &QPushButton::clicked, this, [=]() {
        QString path = IoDialog::get_select_file("(*.csv);;(*.*)");
        if (path.isEmpty()) {
            return;
        }
        ui->LE_Log_Path->setText(path);

        //
        MyTableModelBig* model = ui->ITV_csv->get_table_model();

    });
}
