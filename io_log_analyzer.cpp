#include "io_log_analyzer.h"
#include "ui_io_log_analyzer.h"

IoLogAnalyzer::IoLogAnalyzer(QWidget* parent)
    : IoBaseControls(parent)
    , ui(new Ui::IoLogAnalyzer)
{
    ui->setupUi(this);
    init_connect();
}

IoLogAnalyzer::~IoLogAnalyzer()
{
    // 清理线程资源
    if (thread_) {
        thread_->quit();
        thread_->wait();
        delete thread_;
        thread_ = nullptr;
    }
    if (loading_) {
        delete loading_;
        loading_ = nullptr;
    }

    delete ui;
}

void IoLogAnalyzer::init(QString project)
{
    project_ = project;
    //
    loading_ = new LoadLogThread;
    thread_  = new QThread;
    loading_->moveToThread(thread_);

    // 连接线程信号
    connect(this, &IoLogAnalyzer::sig_start_work, loading_, &LoadLogThread::slot_start_work);
    connect(loading_, &LoadLogThread::sig_progress, this, [=](int current_row) {
        qDebug() << "加载进度:" << current_row << "行";
    });
    connect(loading_, &LoadLogThread::sig_finished, this, [=](int total_rows) {
        qDebug() << "CSV加载完成，总共" << total_rows << "行";
    });

    thread_->start();
}

void IoLogAnalyzer::init_connect()
{
    // 选择日志文件
    connect(ui->PB_Select_File, &QPushButton::clicked, this, [=]() {
        QString path = IoDialog::get_select_file("(*.csv);;(*.*)");
        if (path.isEmpty()) {
            return;
        }
        ui->LE_Log_Path->setText(path);

        // 获取table model
        MyTableModelBig* model = ui->ITV_csv->get_table_model();

        // 设置线程工作参数
        if (loading_ && model) {
            loading_->set_work_params(model, path);
            // 启动CSV加载工作
            emit sig_start_work();
        }
    });
}
