#ifndef IO_LOG_ANALYZER_H
#define IO_LOG_ANALYZER_H

#include "io_custom_component_global.h"
#include <QWidget>
#include <QFileDialog>
#include <QTextStream>
#include <QMessageBox>
#include <QThread>
#include <QDebug>

#include "load_log_thread.h"
#include "../io_dialog/io_dialog.h"
#include "../io_base_controls/io_base_controls.h"


namespace Ui {
class IoLogAnalyzer;
}

class IO_CUSTOM_COMPONENTSHARED_EXPORT IoLogAnalyzer : public IoBaseControls
{
    Q_OBJECT

public:
    explicit IoLogAnalyzer(QWidget* parent = 0);
    ~IoLogAnalyzer();

private:
    Ui::IoLogAnalyzer* ui;

public:
    void init(QString project);

private:
    void init_connect();

signals:
    void sig_start_work();

private:
    QString project_;

    LoadLogThread* loading_ = nullptr;
    QThread*       thread_  = nullptr;
};

#endif   // KTX_PARSING_LOG_H
