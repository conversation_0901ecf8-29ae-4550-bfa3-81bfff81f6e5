<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>IoLogAnalyzer</class>
 <widget class="QWidget" name="IoLogAnalyzer">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1250</width>
    <height>699</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QPushButton
{
	background-color: rgb(225, 225, 225);
	border: 1px solid #7f8585;
	border-radius: 3px;
	min-width:100px;
	max-width:100px;
	min-height:28px;
	max-height:28px;
}
QPushButton:hover 
{
	background-color: #4b6e8a; 
	color:#ffffff;
}

QComboBox 
{
	background-color: #f0f0f0;
	border-radius: 3px; 
	border: 1px solid #7f8585;
	min-width:180px;
	max-width:180px;
	min-height:28px;
	max-height:28px;
}

QComboBox::item 
{
    /*padding: 4px;*/
    text-align: center; 
}

QLineEdit 
{
    border-radius: 3px; 
    border: 1px solid #7f8585; 
    min-height: 28px; 
}

QGroupBox
{
	border:1px solid #7f8585;
}
</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" rowstretch="0,1">
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <property name="spacing">
    <number>2</number>
   </property>
   <item row="0" column="0">
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" rowstretch="0">
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>5</number>
      </property>
      <property name="bottomMargin">
       <number>5</number>
      </property>
      <property name="spacing">
       <number>3</number>
      </property>
      <item row="0" column="13">
       <widget class="QLineEdit" name="LE_Log_Path">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item row="0" column="14">
       <widget class="QPushButton" name="PB_Select_File">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>选择文件</string>
        </property>
       </widget>
      </item>
      <item row="0" column="12">
       <widget class="QLabel" name="LB_Log_Path">
        <property name="layoutDirection">
         <enum>Qt::RightToLeft</enum>
        </property>
        <property name="text">
         <string>文件</string>
        </property>
       </widget>
      </item>
      <item row="0" column="15">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="IoTableViewBig" name="ITV_csv"/>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>IoTableViewBig</class>
   <extends>QTableView</extends>
   <header>../io_custom_widgets/io_table_view/io_table_view_big.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
