#ifndef LOAD_LOG_THREAD_H
#define LOAD_LOG_THREAD_H

#include <QDateTime>
#include <QDebug>
#include <QDesktopServices>
#include <QFile>
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QTextStream>
#include <QThread>
#include <QUrl>
#include <QVector>

#include "../io_custom_widgets/io_table_view/my_table_model_big.h"

class LoadLogThread : public QObject
{
    Q_OBJECT
public:
    explicit LoadLogThread(QObject* parent = 0);

public slots:
    void slot_start_work();

};

#endif
