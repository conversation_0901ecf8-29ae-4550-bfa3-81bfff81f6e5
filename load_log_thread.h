#ifndef LOAD_LOG_THREAD_H
#define LOAD_LOG_THREAD_H

#include <QDateTime>
#include <QDebug>
#include <QDesktopServices>
#include <QFile>
#include <QMap>
#include <QMutex>
#include <QObject>
#include <QTextStream>
#include <QThread>
#include <QUrl>
#include <QVector>

#include "../io_custom_widgets/io_table_view/my_table_model_big.h"

class LoadLogThread : public QObject
{
    Q_OBJECT
public:
    explicit LoadLogThread(QObject* parent = 0);

    // 设置工作参数
    void set_work_params(MyTableModelBig* model, const QString& file_path);

public slots:
    void slot_start_work();

signals:
    void sig_progress(int current_row);
    void sig_finished(int total_rows);

private:
    MyTableModelBig* model_ = nullptr;
    QString path_;
};

#endif
